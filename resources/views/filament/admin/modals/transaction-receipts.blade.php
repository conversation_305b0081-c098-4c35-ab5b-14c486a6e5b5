<div class="space-y-4">
    @if($transaction->receipts->count() > 0)
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Receipt Number') }}
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Date') }}
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Amount') }}
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Status') }}
                        </th>
                        <th class="text-right py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($transaction->receipts as $receipt)
                        <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <td class="py-3 px-4">
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $receipt->receipt_number }}
                                </div>
                                @if($receipt->payment_provider_object_id)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $receipt->payment_provider_object_id }}
                                    </div>
                                @endif
                            </td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">
                                {{ $receipt->created_at->format(config('app.datetime_format')) }}
                            </td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">
                                @if($receipt->amount_paid)
                                    {{ money($receipt->amount_paid, $transaction->currency->code) }}
                                @else
                                    {{ money(abs($transaction->amount), $transaction->currency->code) }}
                                @endif
                            </td>
                            <td class="py-3 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($receipt->receipt_status === 'paid')
                                        bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                    @elseif($receipt->receipt_status === 'pending')
                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                    @elseif($receipt->receipt_status === 'failed')
                                        bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                    @else
                                        bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                    @endif
                                ">
                                    {{ ucfirst($receipt->receipt_status ?? 'unknown') }}
                                </span>
                            </td>
                            <td class="py-3 px-4 text-right">
                                <div class="flex justify-end space-x-2">
                                    <a href="{{ route('invoice_receipt.generate', ['uuid' => $receipt->uuid, 'docType' => 'receipt']) }}"
                                       target="_blank"
                                       class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 rounded-md transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        {{ __('Download') }}
                                    </a>

                                    <a href="{{ route('invoice_receipt.generate', ['uuid' => $receipt->uuid, 'docType' => 'receipt']) }}?regenerate=true"
                                       target="_blank"
                                       class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 bg-gray-50 hover:bg-gray-100 dark:bg-gray-900/20 dark:hover:bg-gray-900/30 rounded-md transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        {{ __('Regenerate') }}
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        {{ __('About Receipts') }}
                    </h4>
                    <p class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                        {{ __('Receipts are generated for successful payments and serve as proof of payment. You can download or regenerate receipts as needed.') }}
                    </p>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {{ __('No receipts found') }}
            </h3>
            <p class="text-gray-500 dark:text-gray-400">
                {{ __('This transaction has no associated receipts.') }}
            </p>
        </div>
    @endif
</div>
