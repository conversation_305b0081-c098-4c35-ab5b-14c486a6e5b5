<div class="space-y-4">
    @if($transaction->receipts->count() > 0)
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Receipt Number') }}
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Date') }}
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Amount') }}
                        </th>
                        <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Status') }}
                        </th>
                        <th class="text-right py-3 px-4 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($transaction->receipts as $receipt)
                        <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <td class="py-3 px-4">
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $receipt->receipt_number }}
                                </div>
                                @if($receipt->payment_provider_object_id)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $receipt->payment_provider_object_id }}
                                    </div>
                                @endif
                            </td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">
                                {{ $receipt->created_at->format(config('app.datetime_format')) }}
                            </td>
                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">
                                @if($receipt->amount_paid)
                                    {{ money($receipt->amount_paid, $transaction->currency->code) }}
                                @else
                                    {{ money(abs($transaction->amount), $transaction->currency->code) }}
                                @endif
                            </td>
                            <td class="py-3 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($receipt->receipt_status === 'paid')
                                        bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                    @elseif($receipt->receipt_status === 'pending')
                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                    @elseif($receipt->receipt_status === 'failed')
                                        bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                    @else
                                        bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                    @endif
                                ">
                                    {{ ucfirst($receipt->receipt_status ?? 'unknown') }}
                                </span>
                            </td>
                            <td class="py-3 px-4 text-right">
                                <div class="flex justify-end space-x-2">
                                    <a href="{{ route('invoice_receipt.generate', ['uuid' => $receipt->uuid, 'docType' => 'receipt']) }}"
                                       target="_blank"
                                       class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 rounded-md transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        {{ __('Download') }}
                                    </a>


                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>


    @else
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {{ __('No receipts found') }}
            </h3>
            <p class="text-gray-500 dark:text-gray-400">
                {{ __('This transaction has no associated receipts.') }}
            </p>
        </div>
    @endif
</div>
