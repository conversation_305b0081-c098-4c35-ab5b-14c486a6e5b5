<?php

namespace App\Filament\Admin\Resources\SubscriptionResource\RelationManagers;

use App\Constants\TransactionStatus;
use App\Mapper\TransactionStatusMapper;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(__('Invoices & Receipts'))
            ->description(__('View all invoices and receipts related to this subscription.'))
            ->recordTitleAttribute('invoice_number')
            ->columns([
                Tables\Columns\TextColumn::make('invoice_number')
                    ->label(__('Invoice Number'))
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage(__('Invoice number copied'))
                    ->copyMessageDuration(1500),

                Tables\Columns\TextColumn::make('payment_provider_reference')
                    ->label(__('Payment Reference'))
                    ->searchable()
                    ->placeholder('-')
                    ->copyable()
                    ->copyMessage(__('Payment reference copied'))
                    ->copyMessageDuration(1500),

                Tables\Columns\TextColumn::make('amount')
                    ->label(__('Amount'))
                    ->formatStateUsing(function (string $state, Transaction $record) {
                        return money(abs($state), $record->currency->code);
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (Transaction $record, TransactionStatusMapper $mapper): string => $mapper->mapColor($record->status))
                    ->formatStateUsing(fn (string $state, TransactionStatusMapper $mapper): string => $mapper->mapForDisplay($state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Date'))
                    ->dateTime(config('app.datetime_format'))
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('receipts_count')
                    ->label(__('Receipts'))
                    ->counts('receipts')
                    ->badge()
                    ->color('info')
                    ->formatStateUsing(fn ($state) => $state > 0 ? $state : '-'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options([
                        TransactionStatus::SUCCESS->value => __('Success'),
                        TransactionStatus::FAILED->value => __('Failed'),
                        TransactionStatus::PENDING->value => __('Pending'),
                        TransactionStatus::REFUNDED->value => __('Refunded'),
                        TransactionStatus::DISPUTED->value => __('Disputed'),
                    ]),
            ])
            ->headerActions([])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('view-invoice')
                        ->label(__('View Invoice'))
                        ->icon('heroicon-o-document-text')
                        ->color('primary')
                        ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                        ->url(
                            fn (Transaction $record): string => route('invoice_receipt.generate', ['transactionUuid' => $record->uuid, 'docType' => 'invoice']),
                            shouldOpenInNewTab: true
                        ),


                ])
                ->tooltip(__('Invoice Actions'))
                ->button()
                ->outlined(),

                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('view-receipts')
                        ->label(__('View Receipts'))
                        ->icon('heroicon-o-receipt-percent')
                        ->color('success')
                        ->visible(fn (Transaction $record): bool => $record->receipts()->count() > 0)
                        ->modalHeading(fn (Transaction $record) => __('Receipts for Invoice :number', ['number' => $record->invoice_number]))
                        ->modalContent(fn (Transaction $record) => view('filament.admin.modals.transaction-receipts', ['transaction' => $record]))
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel(__('Close')),
                ])
                ->tooltip(__('Receipt Actions'))
                ->button()
                ->outlined()
                ->visible(fn (Transaction $record): bool => $record->receipts()->count() > 0),
            ])
            ->bulkActions([])
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'currency',
                'paymentProvider',
                'receipts',
            ]))
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No transactions found'))
            ->emptyStateDescription(__('This subscription has no associated transactions yet.'))
            ->emptyStateIcon('heroicon-o-document-text');
    }
}
