<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\JsonResponse;

class TransactionReceiptsController extends Controller
{
    public function getReceipts(string $transactionId): JsonResponse
    {
        // Ensure user is admin
        if (!auth()->user() || !auth()->user()->isAdmin()) {
            abort(403);
        }
        
        $transaction = Transaction::with(['receipts', 'currency'])->findOrFail($transactionId);
        
        $receiptsData = $transaction->receipts->map(function ($receipt) use ($transaction) {
            return [
                'receipt_number' => $receipt->receipt_number,
                'payment_provider_object_id' => $receipt->payment_provider_object_id,
                'formatted_date' => $receipt->created_at->format(config('app.datetime_format')),
                'formatted_amount' => $receipt->amount_paid 
                    ? money($receipt->amount_paid, $transaction->currency->code)
                    : money(abs($transaction->amount), $transaction->currency->code),
                'receipt_status' => $receipt->receipt_status,
                'download_url' => route('invoice_receipt.generate', ['uuid' => $receipt->uuid, 'docType' => 'receipt']),
                'regenerate_url' => route('invoice_receipt.generate', ['uuid' => $receipt->uuid, 'docType' => 'receipt']) . '?regenerate=true',
            ];
        });
        
        return response()->json([
            'invoice_number' => $transaction->invoice_number,
            'currency' => $transaction->currency->code,
            'receipts' => $receiptsData,
        ]);
    }
}
