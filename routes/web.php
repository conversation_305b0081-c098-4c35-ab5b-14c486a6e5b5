<?php

use App\Http\Controllers\LanguageController;
use App\Http\Controllers\Auth\OAuthController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CmsController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\ReceiptController;
use App\Http\Controllers\PaymentProviders\PaddleController;
use App\Http\Controllers\RoadmapController;
use App\Http\Middleware\TranslationRoutingMiddleware;
use App\Models\Config;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AccountController;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\LogoutController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\CustomLivewireController;
use Illuminate\Auth\Events\Verified;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Crypt;
use App\Services\TranslationApiService;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
| If you want the URL to be added to the sitemap, add a "sitemapped" middleware to the route (it has to GET route)
|
*/

// Route::get('/', function () {
//     $pvgisApi = config("services.pvgis.api");

//     $deviationThresholdGreen = Config::get("deviation_threshold_green_percent");
//     $deviationThresholdOrange = Config::get("deviation_threshold_orange_percent");

//     return view('home', [
//         'pvgisApi' => $pvgisApi,
//         'deviationThresholdGreen' => $deviationThresholdGreen,
//         'deviationThresholdOrange' => $deviationThresholdOrange,
//     ]);
// })->name('home')->middleware('sitemapped');

// Route::get('/', [App\Http\Controllers\SubscriptionController::class, 'page'])
//     ->name('home')
//     ->middleware('sitemapped');

Route::post('/custom/livewire/upload-file', [CustomLivewireController::class, 'uploadFile'])
    ->name('livewire.upload-file')
    ->middleware('web');

Route::post('/cross-logout', [LogoutController::class, 'destroy'])->name('cross-logout');

Route::get('/logout-session', function (Request $request) {
    Auth::logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();

    return redirect()->away(config('app.url') . '/login');
});

Route::domain(env('SUBDOMAIN_DASHBOARD'))->middleware('web')->group(function () {
    Route::redirect('/', '/dashboard');

    Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard.index')->middleware('auth');

    Route::get('/create-project', [\App\Http\Controllers\DashboardController::class, 'createProject'])->name('dashboard.create-project')->middleware('auth');

    Route::get('/project-info/{id}', [\App\Http\Controllers\DashboardController::class, 'projectInfo'])->name('dashboard.project-info')->middleware('auth');

    Route::get('/project/{id}/simulation', [\App\Http\Controllers\DashboardController::class, 'createSimulation'])->name('dashboard.create-simulation')->middleware('auth');

    Route::get('/project/{id}/simulation/pdf', [\App\Http\Controllers\DashboardController::class, 'pdfSimulation'])->name('dashboard.pdf-simulation')->middleware('auth');

    Route::get('/project/{id}/simulation/{simId}', [\App\Http\Controllers\DashboardController::class, 'simulationInfo'])->name('dashboard.simulation-info')->middleware('auth');

    Route::get('/project/{id}/simulation/{simId}/pdf', [\App\Http\Controllers\DashboardController::class, 'pdfSimulation'])->name('dashboard.pdf-simulation-info')->middleware('auth');

    Route::get('/login-with-token', [LoginController::class, 'dashboardLogin']);
});

Route::get('/coming-soon', function () {
    return view('errors.coming-soon');
})->name('coming-soon');

// Not found
Route::get('/not-found', fn() => view('errors.404'))
    ->name('not-found');

Auth::routes();

Route::get('/email/verify', function () {
    return view('auth.verify');
})->middleware('auth')->name('verification.notice');

Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();

    $user = $request->user();
    if ($user->hasVerifiedEmail()) {
        return redirect()->route('registration.thank-you');
    }

    return redirect('/');
})->middleware(['auth', 'signed'])->name('verification.verify');

Route::get('/email/verify/{id}/{hash}', function (Request $request, $id, $hash) {
    // Manually check signature
    if (!URL::hasValidSignature($request)) {
        return redirect()->route('login')->withErrors([
            'account' => __("The verification link may have already been used or has expired. Please request a new one."),
        ]);
    }

    $user = User::findOrFail($id);

    // Manually validate the hash
    if (!hash_equals((string) $hash, sha1($user->getEmailForVerification()))) {
        return redirect()->route('login')->withErrors([
            'account' => "The action provided by this URL may have already been performed or has expired. If it has expired, please request a new verification email.",
        ]);
    }

    // Mark email as verified if not already
    if (!$user->hasVerifiedEmail()) {
        $user->markEmailAsVerified();
        event(new Verified($user));
    }

    // Log the user automatically
    Auth::login($user);

    return redirect()->route('registration.thank-you');
})->name('verification.verify');

Route::get('/email/verify/{token}', function (Request $request, $token) {
    try {
        $data = Crypt::decrypt($token);

        // Check expiry
        if (now()->timestamp > $data['expires_at'] || $data['process'] !== 'email_verfication') {
            return redirect()->route('login')->withErrors([
                'account' => __("The verification link may have already been used or has expired. Please request a new one."),
            ]);
        }

        $user = User::findOrFail($data['user_id']);

        if (!$user->hasVerifiedEmail()) {
            $user->markEmailAsVerified();
            event(new Verified($user));
        }

        Auth::login($user);

        return redirect()->route('registration.thank-you');

    } catch (\Exception $e) {
        return redirect()->route('login')->withErrors([
            'account' => __("Invalid or expired verification link. Please request a new one."),
        ]);
    }
})->name('verification.verify-email');

Route::get('/phone/verify', function () {
    return view('verify.sms-verification');
})->name('user.phone-verify')
    ->middleware('auth');

Route::get('/phone/verified', function () {
    return view('verify.sms-verification-success');
})->name('user.phone-verified')
    ->middleware('auth');

Route::post('/email/verification-notification', function (\Illuminate\Http\Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('sent');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');

Route::get('/registration/thank-you', function () {
    return view('auth.thank-you');
})->name('registration.thank-you');

Route::get('/auth/{provider}/redirect', [OAuthController::class, 'redirect'])
    ->where('provider', 'google|github|facebook|twitter-oauth-2|linkedin-openid|bitbucket|gitlab')
    ->name('auth.oauth.redirect');

Route::get('/auth/{provider}/callback', [OAuthController::class, 'callback'])
    ->where('provider', 'google|github|facebook|twitter-oauth-2|linkedin-openid|bitbucket|gitlab')
    ->name('auth.oauth.callback');

// CHECKOUT ROUTES

Route::get('/checkout/plan/{planSlug}', [
    App\Http\Controllers\SubscriptionCheckoutController::class,
    'subscriptionCheckout',
])->name('checkout.subscription');


Route::get('/checkout/{stepSlug}', [
    App\Http\Controllers\SubscriptionCheckoutController::class,
    'subscriptionCheckoutDisplay',
])->name('checkout.cart_wizard');


// Route::get('/{lang}/checkout/plan/{planSlug}', [App\Http\Controllers\SubscriptionCheckoutController::class, 'subscriptionCheckout',])
// ->middleware([TranslationRoutingMiddleware::class]);
// Route::get('/checkout/plan/{planSlug}', [App\Http\Controllers\SubscriptionCheckoutController::class, 'subscriptionCheckout',])
// ->middleware([TranslationRoutingMiddleware::class]);

// Route::get('/{lang}/checkout/change/{planSlug}', [App\Http\Controllers\SubscriptionCheckoutController::class, 'subscriptionCheckoutChange'])
// ->middleware([TranslationRoutingMiddleware::class]);
// Route::get('/checkout/change/{planSlug}', [App\Http\Controllers\SubscriptionCheckoutController::class, 'subscriptionCheckoutChange'])
// ->middleware([TranslationRoutingMiddleware::class]);

// CHECKOUT ROUTES

Route::get('/checkout/convert-subscription/{subscriptionUuid}', [
    App\Http\Controllers\SubscriptionCheckoutController::class,
    'convertLocalSubscriptionCheckout',
])->name('checkout.convert-local-subscription');

Route::get('/already-subscribed', function () {
    return view('checkout.already-subscribed');
})->name('checkout.subscription.already-subscribed');

Route::get('/checkout/subscription/success', [
    App\Http\Controllers\SubscriptionCheckoutController::class,
    'subscriptionCheckoutSuccess',
])->name('checkout.subscription.success')->middleware('auth');

Route::get('/checkout/convert-subscription-success', [
    App\Http\Controllers\SubscriptionCheckoutController::class,
    'convertLocalSubscriptionCheckoutSuccess',
])->name('checkout.convert-local-subscription.success')->middleware('auth');

Route::get('/payment-provider/paddle/payment-link', [
    PaddleController::class,
    'paymentLink',
])->name('payment-link.paddle');

Route::get('/subscription/{subscriptionUuid}/change-plan/{planSlug}', [
    App\Http\Controllers\SubscriptionController::class,
    'changePlan',
])->name('subscription.change-plan')->middleware('auth');

Route::post('/subscription/{subscriptionUuid}/change-plan/{planSlug}', [
    App\Http\Controllers\SubscriptionController::class,
    'changePlan',
])->name('subscription.change-plan.post')->middleware('auth');

Route::get('/subscription/change-plan-thank-you', [
    App\Http\Controllers\SubscriptionController::class,
    'success',
])->name('subscription.change-plan.thank-you')->middleware('auth');

// Route::get('/subscription', [ App\Http\Controllers\SubscriptionController::class, 'page']);
Route::get("/{lang}/subscription", [App\Http\Controllers\SubscriptionController::class, 'page'])
->middleware([TranslationRoutingMiddleware::class]);
Route::get("/subscription", [App\Http\Controllers\SubscriptionController::class, 'page'])
->middleware([TranslationRoutingMiddleware::class]);

// blog
Route::controller(BlogController::class)
    ->prefix('/blog')
    ->group(function () {
        Route::get('/', 'all')->name('blog')->middleware('sitemapped');
        Route::get('/category/{slug}', 'category')->name('blog.category');
        Route::get('/{slug}', 'view')->name('blog.view');
    });

Route::get('/terms-of-service', function () {
    return view('pages.terms-of-service-page');
})->name('terms-of-service')->middleware('sitemapped');

Route::get('/privacy-policy', function () {
    return view('pages.privacy-policy-page');
})->name('privacy-policy')->middleware('sitemapped');

// Product checkout routes

Route::get('/buy/product/{productSlug}/{quantity?}', [
    App\Http\Controllers\ProductCheckoutController::class,
    'addToCart',
])->name('buy.product');

Route::get('/cart/clear', [
    App\Http\Controllers\ProductCheckoutController::class,
    'clearCart',
])->name('cart.clear');

Route::get('/checkout/product', [
    App\Http\Controllers\ProductCheckoutController::class,
    'productCheckout',
])->name('checkout.product');

Route::get('/checkout/product/success', [
    App\Http\Controllers\ProductCheckoutController::class,
    'productCheckoutSuccess',
])->name('checkout.product.success')->middleware('auth');

// roadmap

Route::controller(RoadmapController::class)
    ->prefix('/roadmap')
    ->group(function () {
        Route::get('/', 'index')->name('roadmap');
        Route::get('/i/{itemSlug}', 'viewItem')->name('roadmap.viewItem');
        Route::get('/suggest', 'suggest')->name('roadmap.suggest')->middleware('auth');
    });

// Invoice

Route::controller(InvoiceController::class)
    ->prefix('/invoice')
    ->group(function () {
        Route::get('/generate/{docType}/{uuid}', 'generatePdf')->name('invoice_receipt.generate');
        // Route::get('/preview', 'preview')->name('invoice.preview');
    });

// Admin Transaction Receipts API
Route::get('/admin/transactions/{transactionId}/receipts', [App\Http\Controllers\Admin\TransactionReceiptsController::class, 'getReceipts'])
    ->middleware('auth')
    ->name('admin.transactions.receipts');

// Receipt
// Route::controller(ReceiptController::class)
//     ->prefix('/receipt')
//     ->group(function () {
//         Route::get('/generate/{transactionUuid}', 'generate')->name('receipt.generate');
//     });

// Language
Route::get('/lang/{language}', [LanguageController::class, 'setLanguage'])->name('language');
// ->middleware([TranslationRoutingMiddleware::class]);

Route::get("/{lang}/control", [CmsController::class, 'controlPage'])
->middleware([TranslationRoutingMiddleware::class]);
Route::get("/control", [CmsController::class, 'controlPage'])
->middleware([TranslationRoutingMiddleware::class]);

Route::get("/{lang}/expertise", [CmsController::class, 'expertisePage'])
    ->middleware([TranslationRoutingMiddleware::class]);
Route::get("/expertise", [CmsController::class, 'expertisePage'])
    ->middleware([TranslationRoutingMiddleware::class]);

Route::get('/reactivate-account', [AccountController::class, 'reactivate'])
    ->name('account.reactivate');


/* TESTING ROUTES */
Route::get('/test-cms', [CmsController::class, 'testCms'])
    ->name('test-cms');

Route::get('/test-pdf/{path}', function ($path) {
    return view($path);
})->name('test-pdf');


// Route::get('/', function (Request $request) {
//     $supportedLanguages = ['en', 'fr', 'de', 'es']; // Add your supported languages
//     $locale = $request->getPreferredLanguage($supportedLanguages) ?? 'en';
//     return redirect()->route('home', ['lang' => $locale]);
// });

// Route::get('/{lang}', [CmsController::class, 'showHomepage'])
//     ->where('lang', '[a-z]{2}')
//     ->name('home');

// Route::get('/', [CmsController::class, 'showHomepage'])
//     ->where('lang', '[a-z]{2}')
//     ->name('home');

Route::get('/', function (Request $request, TranslationApiService $translationService) {
    $result = $translationService->useIpBasedLanguage(null, $request);
    return redirect($result["url"], $result["statusCode"]);
})
->middleware([TranslationRoutingMiddleware::class])
->name('home');

Route::get('/register', [RegisterController::class, 'showRegistrationForm'])
    ->name('register');

Route::get('/login', [LoginController::class, 'showLoginForm'])
    ->name('login');


Route::domain(env('APP_DOMAIN'))->middleware('web')->group(function () {
    Route::get('/{lang}', [CmsController::class, 'showHomepage'])
        ->where('lang', '[a-z]{2}')
        ->name('home-lang'); // Give it a unique name

    Route::get('/{lang}/{cms_url}', [CmsController::class, 'handleRequestCms'])
        ->where('lang', '[a-z]{2}')
        ->where('cms_url', '.*')
        ->name('cms')
        ->middleware([TranslationRoutingMiddleware::class]);

    Route::get('/{cms_url}', [CmsController::class, 'handleRequestCms'])
        ->where('cms_url', '.*')
        ->name('cms')
        ->middleware([TranslationRoutingMiddleware::class]);
});




// Route::get('/', function (Request $request) {
//     $supportedLanguages = ['en', 'fr', 'de', 'es']; // Add your supported languages
//     $locale = $request->getPreferredLanguage($supportedLanguages) ?? 'en';
//     return redirect()->route('home', ['lang' => $locale]);
// });


// Route::get('/{lang}/{cms_url}', [CmsController::class, 'handleRequestCms'])
//     ->where('lang', '[a-z]{2}')
//     ->where('cms_url', '.*') // Good practice to allow slashes in the URL
//     ->name('cms')
//     ->middleware([TranslationRoutingMiddleware::class]);

// Route::get('/{cms_url}', [CmsController::class, 'handleRequestCms'])
//     ->where('cms_url', '.*') // Good practice to allow slashes in the URL
//     ->name('cms')
//     ->middleware([TranslationRoutingMiddleware::class]);


